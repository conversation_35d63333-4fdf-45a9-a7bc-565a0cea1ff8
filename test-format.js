// 格式化功能测试脚本
// 在浏览器控制台中运行此脚本来测试格式化功能

console.log('🧪 开始测试格式化功能...');

// 测试HTML转换为格式化文本
function testFormatting() {
    // 创建测试HTML
    const testHTML = `
        <div>
            <h1>测试标题</h1>
            <p>这是一个<strong>粗体</strong>和<em>斜体</em>的段落。</p>
            <ul>
                <li>列表项 1</li>
                <li>列表项 2
                    <ul>
                        <li>嵌套项目</li>
                    </ul>
                </li>
            </ul>
            <pre><code>function test() {
    console.log('代码块测试');
}</code></pre>
            <blockquote>这是一个引用块</blockquote>
        </div>
    `;
    
    // 创建临时元素
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = testHTML;
    
    // 测试格式化函数
    if (typeof window.htmlToFormattedText === 'function') {
        const result = window.htmlToFormattedText(tempDiv);
        console.log('✅ 格式化结果：');
        console.log(result);
        console.log('---');
        return result;
    } else {
        console.error('❌ htmlToFormattedText 函数未找到');
        return null;
    }
}

// 测试选中内容获取
function testSelection() {
    console.log('📝 请在页面上选择一些文本，然后运行 testSelectedContent()');
}

function testSelectedContent() {
    if (typeof getSelectedContent === 'function') {
        const result = getSelectedContent();
        if (result) {
            console.log('✅ 选中内容：');
            console.log('原始文本：', result.text);
            console.log('格式化文本：', result.formattedText);
            console.log('长度：', result.length);
        } else {
            console.log('❌ 没有选中内容');
        }
    } else {
        console.error('❌ getSelectedContent 函数未找到');
    }
}

// 运行测试
testFormatting();
testSelection();

// 导出测试函数到全局
window.testSelectedContent = testSelectedContent;
window.testFormatting = testFormatting;

console.log('🎯 测试完成！你可以：');
console.log('1. 选择页面文本后运行 testSelectedContent() 测试选中内容');
console.log('2. 运行 testFormatting() 重新测试格式化功能');
