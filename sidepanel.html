<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>Flomo 保存工具</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f8f9fa;
      font-size: 14px;
      line-height: 1.5;
      height: 100vh;
      overflow: hidden;
      box-sizing: border-box;
    }

    .container {
      display: flex;
      flex-direction: column;
      height: calc(100vh - 40px);
      max-width: 100%;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      flex-shrink: 0;
      padding: 20px;
      background: white;
      border-bottom: 1px solid #f0f0f0;
    }

    h1 {
      margin: 0;
      font-size: 18px;
      color: #333;
      text-align: center;
      font-weight: 600;
    }

    .content-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 20px;
      overflow: hidden;
    }

    .content-info {
      flex-shrink: 0;
      margin-bottom: 16px;
    }

    .textarea-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }

    .actions-area {
      flex-shrink: 0;
      padding: 0 20px 20px 20px;
      background: white;
      border-top: 1px solid #e0e0e0;
    }

    .content-info {
      background: #e8f4fd;
      border: 1px solid #3b82f6;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      font-size: 13px;
      color: #1e40af;
      box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1);
      position: relative;
      border-left: 4px solid #3b82f6;
    }

    .content-info::after {
      content: 'ℹ️';
      position: absolute;
      right: 12px;
      top: 12px;
      font-size: 14px;
      opacity: 0.7;
    }

    .info-row {
      display: flex;
      align-items: flex-start;
      margin-bottom: 8px;
      gap: 8px;
    }

    .info-row:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-weight: 500;
      color: #1e40af;
      min-width: 40px;
      flex-shrink: 0;
      font-size: 12px;
    }

    .info-content {
      flex: 1;
      word-wrap: break-word;
      line-height: 1.4;
      color: #1e40af;
      font-size: 14px;
    }

    .page-title {
      font-weight: 500;
      color: #1f2937;
    }

    .page-url {
      display: inline-block;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: help;
      position: relative;
      color: #1d4ed8;
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
    }

    .page-url:hover {
      text-decoration: underline;
      color: #1e40af;
    }

    .page-url-tooltip {
      position: absolute;
      bottom: 100%;
      left: 0;
      right: 0;
      background: #1f2937;
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      word-break: break-all;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s, visibility 0.3s;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      max-height: 120px;
      overflow-y: auto;
      font-family: 'Monaco', 'Menlo', monospace;
      line-height: 1.3;
    }

    .page-url:hover .page-url-tooltip {
      opacity: 1;
      visibility: visible;
    }

    .page-url-tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 20px;
      border: 6px solid transparent;
      border-top-color: #1f2937;
    }

    textarea {
      width: 100%;
      flex: 1;
      min-height: 120px;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-family: inherit;
      resize: none;
      box-sizing: border-box;
    }

    textarea:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
    }

    .ai-tools {
      display: flex;
      gap: 16px;
      margin: 16px 0;
      flex-wrap: wrap;
    }

    .ai-btn {
      flex: 1;
      padding: 12px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .ai-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s;
    }

    .ai-btn:hover::before {
      left: 100%;
    }

    .ai-btn-tags {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
    }

    .ai-btn-tags:hover {
      background: linear-gradient(135deg, #d97706, #b45309);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
    }

    .ai-btn-translate {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
    }

    .ai-btn-translate:hover {
      background: linear-gradient(135deg, #059669, #047857);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
    }

    .ai-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    .ai-btn:disabled::before {
      display: none;
    }

    .save-btn {
      width: 100%;
      padding: 12px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      min-height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;
      background: #1a73e8;
      color: white;
    }

    .save-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s;
    }

    .save-btn:hover::before {
      left: 100%;
    }

    .save-btn:hover {
      background: #1557b0;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(26, 115, 232, 0.3);
    }

    .save-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    .save-btn:disabled::before {
      display: none;
    }

    /* Toast消息系统 */
    .toast-container {
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      pointer-events: none;
      width: calc(100% - 40px);
      max-width: 320px;
    }

    .toast {
      background: white;
      border-radius: 8px;
      padding: 12px 16px;
      margin-bottom: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border-left: 4px solid #ccc;
      font-size: 13px;
      line-height: 1.4;
      opacity: 0;
      transform: translateY(-20px);
      transition: all 0.3s ease;
      pointer-events: auto;
      position: relative;
      word-wrap: break-word;
    }

    .toast.show {
      opacity: 1;
      transform: translateY(0);
    }

    .toast.success {
      border-left-color: #34a853;
      background: linear-gradient(135deg, #e6f4ea, #f0f9f1);
      color: #137333;
    }

    .toast.error {
      border-left-color: #ea4335;
      background: linear-gradient(135deg, #fce8e6, #fef1f0);
      color: #d93025;
    }

    .toast.info {
      border-left-color: #1a73e8;
      background: linear-gradient(135deg, #e8f0fe, #f1f5ff);
      color: #1a73e8;
    }

    .toast.loading {
      border-left-color: #ff9800;
      background: linear-gradient(135deg, #fff3e0, #fff8f1);
      color: #e65100;
    }

    .toast-close {
      position: absolute;
      top: 8px;
      right: 8px;
      background: none;
      border: none;
      font-size: 16px;
      cursor: pointer;
      color: inherit;
      opacity: 0.6;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;
    }

    .toast-close:hover {
      opacity: 1;
      background: rgba(0, 0, 0, 0.1);
    }

    .toast-icon {
      display: inline-block;
      margin-right: 8px;
      font-size: 14px;
    }

    .toast-message {
      padding-right: 24px;
    }

    /* 加载动画 */
    .toast.loading .toast-icon {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    /* 隐藏原有的状态消息 */
    .status {
      display: none !important;
    }

    .loading {
      opacity: 0.7;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #ccc;
      border-top: 2px solid #1a73e8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 8px;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>
</head>

<body>
  <!-- Toast消息容器 -->
  <div class="toast-container" id="toast-container"></div>

  <div class="container">
    <!-- 头部区域 -->
    <div class="header">
      <h1>📝 Flomo 保存工具</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <div id="content-info" class="content-info" style="display: none;">
        <div class="info-row">
          <div class="info-label">来源</div>
          <div class="info-content">
            <span id="page-title" class="page-title"></span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-label">链接</div>
          <div class="info-content">
            <span id="page-url" class="page-url">
              <span class="page-url-tooltip" id="page-url-tooltip"></span>
            </span>
          </div>
        </div>
      </div>

      <div class="textarea-container">
        <textarea id="content-text" placeholder="选择页面文字后，内容会自动显示在这里..."></textarea>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <div class="actions-area">
      <div class="ai-tools" id="ai-tools" style="display: none;">
        <button class="ai-btn ai-btn-tags" id="generate-tags">🏷️ 智能标签</button>
        <button class="ai-btn ai-btn-translate" id="translate-text">🌐 中英对照</button>
      </div>

      <button class="save-btn" id="save-to-flomo">保存到 Flomo</button>
    </div>
  </div>

  <script src="sidepanel.js"></script>
</body>

</html>