// Popup专用配置文件
// 不包含ES6模块语法，可直接在popup.html中使用

const CONFIG_SCHEMA = {
  // AI服务配置
  ai: {
    // 默认配置
    defaults: {
      provider: 'openrouter',
      model: 'deepseek/deepseek-chat-v3-0324:free',
      temperature: 0.7,
      maxTokens: 1000,
      timeout: 60000
    },

    // 支持的AI服务提供商配置
    providers: {
      siliconflow: {
        name: '硅基流动',
        baseUrl: 'https://api.siliconflow.cn/v1',
        models: [
          'Qwen/Qwen2.5-7B-Instruct',
          'THUDM/glm-4-9b-chat',
          'Qwen/Qwen3-8B'
        ],
        keyFormat: /^sk-[a-zA-Z0-9]{48}$/,
        description: '硅基流动提供高质量的开源模型API服务'
      },
      openrouter: {
        name: 'OpenRouter',
        baseUrl: 'https://openrouter.ai/api/v1',
        models: [
          'deepseek/deepseek-chat-v3-0324:free',
          'moonshotai/kimi-k2:free'
        ],
        keyFormat: /^sk-or-[a-zA-Z0-9-]+$/,
        description: 'OpenRouter聚合了多个AI模型提供商，支持免费和付费模型'
      },
      deepseek: {
        name: 'DeepSeek',
        baseUrl: 'https://api.deepseek.com/v1',
        models: [
          'deepseek-chat',
          'deepseek-coder'
        ],
        keyFormat: /^sk-[a-zA-Z0-9]{48}$/,
        description: 'DeepSeek提供强大的对话和编程模型'
      },
      moonshot: {
        name: 'Moonshot AI',
        baseUrl: 'https://api.moonshot.cn/v1',
        models: [
          'moonshot-v1-8k',
          'moonshot-v1-32k',
          'moonshot-v1-128k'
        ],
        keyFormat: /^sk-[a-zA-Z0-9]{48}$/,
        description: 'Moonshot AI（Kimi）提供不同上下文长度的对话模型'
      },
      doubao: {
        name: '豆包',
        baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
        models: [
          'doubao-lite-4k',
          'doubao-lite-32k',
          'doubao-lite-128k',
          'doubao-pro-4k',
          'doubao-pro-32k',
          'doubao-pro-128k'
        ],
        keyFormat: /^[a-zA-Z0-9-_]{20,}$/,
        description: '字节跳动豆包大模型，提供多种规格的对话模型'
      },
      openai: {
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        models: [
          'gpt-3.5-turbo',
          'gpt-4',
          'gpt-4-turbo',
          'gpt-4o',
          'gpt-4o-mini'
        ],
        keyFormat: /^sk-[a-zA-Z0-9]{48,}$/,
        description: 'OpenAI提供的GPT系列模型，包括最新的GPT-4o'
      },
      anthropic: {
        name: 'Anthropic',
        baseUrl: 'https://api.anthropic.com/v1',
        models: [
          'claude-3-haiku-20240307',
          'claude-3-sonnet-20240229',
          'claude-3-opus-20240229',
          'claude-3-5-sonnet-20241022'
        ],
        keyFormat: /^sk-ant-[a-zA-Z0-9-_]{95,}$/,
        description: 'Anthropic提供的Claude系列模型，擅长对话和分析'
      }
    },

    // 验证规则
    validation: {
      provider: (value) => {
        const validProviders = Object.keys(CONFIG_SCHEMA.ai.providers);
        return validProviders.includes(value);
      },
      model: (value, provider) => {
        if (!provider) return false;
        const providerConfig = CONFIG_SCHEMA.ai.providers[provider];
        return providerConfig && providerConfig.models.includes(value);
      },
      apiKey: (value, provider) => {
        if (!value || !provider) return false;
        const providerConfig = CONFIG_SCHEMA.ai.providers[provider];
        return providerConfig && providerConfig.keyFormat.test(value);
      },
      temperature: (value) => {
        return typeof value === 'number' && value >= 0 && value <= 1;
      },
      maxTokens: (value) => {
        return typeof value === 'number' && value > 0 && value <= 10000;
      }
    }
  },

  // Flomo配置
  flomo: {
    defaults: {
      apiUrl: '',
      timeout: 15000
    },

    validation: {
      apiUrl: (value) => {
        if (!value) return false;
        try {
          const url = new URL(value);
          return url.protocol === 'https:' && url.hostname.includes('flomo');
        } catch {
          return false;
        }
      }
    }
  },

  // 侧边栏配置
  sidepanel: {
    defaults: {
      autoClose: true,
      autoCloseDelay: 1000,
      theme: 'light',
      fontSize: 'medium'
    },

    validation: {
      autoClose: (value) => typeof value === 'boolean',
      autoCloseDelay: (value) => {
        return typeof value === 'number' && value >= 0 && value <= 10000;
      },
      theme: (value) => ['light', 'dark', 'auto'].includes(value),
      fontSize: (value) => ['small', 'medium', 'large'].includes(value)
    }
  },

  // UI配置
  ui: {
    defaults: {
      language: 'zh-CN',
      enableAnimations: true,
      enableSounds: false,
      compactMode: false
    },

    validation: {
      language: (value) => ['zh-CN', 'en-US'].includes(value),
      enableAnimations: (value) => typeof value === 'boolean',
      enableSounds: (value) => typeof value === 'boolean',
      compactMode: (value) => typeof value === 'boolean'
    }
  }
};

// 配置优先级定义
const CONFIG_PRIORITY = {
  ENVIRONMENT: 3,  // 环境变量配置（最高优先级）
  USER: 2,         // 用户设置
  DEFAULTS: 1      // 默认配置（最低优先级）
};

// 配置存储键名映射
const STORAGE_KEYS = {
  ai: 'aiConfig',
  flomo: 'flomoConfig',
  sidepanel: 'sidepanelConfig',
  ui: 'uiConfig'
};

// 配置验证工具函数
class ConfigValidator {
  static validate(section, key, value, context = {}) {
    const schema = CONFIG_SCHEMA[section];
    if (!schema || !schema.validation || !schema.validation[key]) {
      return { valid: true };
    }

    try {
      const validator = schema.validation[key];
      const isValid = validator(value, context.provider);

      return {
        valid: isValid,
        errors: isValid ? [] : [`${section}.${key} 验证失败`]
      };
    } catch (error) {
      return {
        valid: false,
        errors: [`${section}.${key} 验证出错: ${error.message}`]
      };
    }
  }

  static validateSection(section, config, context = {}) {
    const schema = CONFIG_SCHEMA[section];
    if (!schema || !schema.validation) {
      return { valid: true, errors: [] };
    }

    const errors = [];
    for (const [key, value] of Object.entries(config)) {
      const result = this.validate(section, key, value, context);
      if (!result.valid) {
        errors.push(...result.errors);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static getDefaults(section) {
    const schema = CONFIG_SCHEMA[section];
    return schema ? { ...schema.defaults } : {};
  }
}

console.log('✅ Popup配置文件已加载'); 