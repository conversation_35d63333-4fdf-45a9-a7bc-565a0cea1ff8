// 简化的侧边栏脚本
document.addEventListener('DOMContentLoaded', async () => {
  const contentText = document.getElementById('content-text');
  const contentInfo = document.getElementById('content-info');
  const pageTitle = document.getElementById('page-title');
  const pageUrl = document.getElementById('page-url');
  const saveButton = document.getElementById('save-to-flomo');
  const generateTagsBtn = document.getElementById('generate-tags');
  const translateBtn = document.getElementById('translate-text');
  const aiToolsContainer = document.getElementById('ai-tools');

  // 调试：检查关键元素是否正确获取
  console.log('🔍 Elements check:', {
    contentText: !!contentText,
    contentInfo: !!contentInfo,
    pageTitle: !!pageTitle,
    pageUrl: !!pageUrl,
    saveButton: !!saveButton,
    aiToolsContainer: !!aiToolsContainer
  });

  // 检查关键元素是否存在
  if (!contentText || !contentInfo || !pageTitle || !pageUrl || !saveButton) {
    console.error('❌ 关键元素缺失，无法初始化侧边栏');
    return;
  }

  // AI功能互斥状态管理
  let currentAIOperation = null;

  // 检查AI功能开关状态
  async function checkAIEnabledStatus() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.sync.get(['aiEnabled']);
        return result.aiEnabled !== undefined ? result.aiEnabled : true; // 默认开启
      } else {
        const stored = localStorage.getItem('aiEnabled');
        return stored !== null ? JSON.parse(stored) : true; // 默认开启
      }
    } catch (error) {
      console.warn('⚠️ 获取AI功能开关状态失败:', error);
      return true; // 出错时默认开启
    }
  }

  // 控制AI按钮的显示/隐藏（基于开关状态和配置完整性）
  async function updateAIButtonsVisibility(isAIConfigured = null) {
    if (!aiToolsContainer) {
      console.error('❌ AI Tools Container not found!');
      return;
    }

    // 检查AI功能开关状态
    const aiEnabled = await checkAIEnabledStatus();

    // 如果没有传入配置状态，则检查当前配置
    if (isAIConfigured === null) {
      isAIConfigured = await ensureAIConfig();
    }

    // 显示逻辑：开关开启时显示按钮，配置不完整时按钮会有视觉提示
    if (aiEnabled) {
      aiToolsContainer.style.display = 'flex';

      // 根据配置状态设置按钮样式
      const buttons = aiToolsContainer.querySelectorAll('button');
      buttons.forEach(button => {
        if (isAIConfigured) {
          button.classList.remove('config-incomplete');
          button.title = '';
        } else {
          button.classList.add('config-incomplete');
          button.title = 'AI配置不完整，点击查看详情';
        }
      });

      if (isAIConfigured) {
        console.log('✅ AI按钮已显示 (开关:开启, 配置:完整)');
      } else {
        console.log('⚠️ AI按钮已显示 (开关:开启, 配置:不完整)');
      }
    } else {
      aiToolsContainer.style.display = 'none';
      console.log('🚫 AI按钮已隐藏 (开关:关闭)');
    }

    return aiEnabled && isAIConfigured;
  }

  // 处理URL显示，包括截断和悬停提示
  function updateUrlDisplay(url) {
    if (!url || url === '未知链接') {
      pageUrl.textContent = '未知链接';
      return;
    }

    const pageUrlTooltip = document.getElementById('page-url-tooltip');

    // 截断长URL用于显示
    let displayUrl = url;
    if (url.length > 50) {
      // 尝试提取域名和路径的关键部分
      try {
        const urlObj = new URL(url);
        const domain = urlObj.hostname;
        const path = urlObj.pathname + urlObj.search;

        if (path.length > 20) {
          displayUrl = domain + path.substring(0, 15) + '...';
        } else {
          displayUrl = domain + path;
        }
      } catch (e) {
        // 如果URL解析失败，使用简单截断
        displayUrl = url.substring(0, 45) + '...';
      }
    }

    pageUrl.textContent = displayUrl;

    // 设置完整URL到tooltip
    if (pageUrlTooltip) {
      pageUrlTooltip.textContent = url;
    }

    // 存储完整URL用于保存时使用
    pageUrl.setAttribute('data-full-url', url);
  }

  // Toast消息系统
  let toastContainer;
  let toastCounter = 0;

  // 初始化toast容器
  function initToastContainer() {
    toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
      console.error('Toast container not found');
    }
  }

  // 显示toast消息
  function showToast(message, type = 'info', duration = 3000) {
    if (!toastContainer) {
      initToastContainer();
    }

    const toastId = `toast-${++toastCounter}`;
    const iconMap = {
      success: '✅',
      error: '❌',
      info: 'ℹ️',
      loading: '⏳'
    };

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.id = toastId;
    toast.innerHTML = `
      <span class="toast-icon">${iconMap[type] || iconMap.info}</span>
      <span class="toast-message">${message}</span>
      <button class="toast-close" onclick="removeToast('${toastId}')" title="关闭">×</button>
    `;

    toastContainer.appendChild(toast);

    // 触发显示动画
    setTimeout(() => {
      toast.classList.add('show');
    }, 10);

    // 自动移除（除了loading类型）
    if (type !== 'loading' && duration > 0) {
      setTimeout(() => {
        removeToast(toastId);
      }, duration);
    }

    return toastId;
  }

  // 移除toast消息
  function removeToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
      toast.classList.remove('show');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }
  }

  // 移除所有toast消息
  function clearAllToasts() {
    if (toastContainer) {
      const toasts = toastContainer.querySelectorAll('.toast');
      toasts.forEach(toast => {
        removeToast(toast.id);
      });
    }
  }

  // 显示加载状态toast
  function showLoadingToast(message) {
    return showToast(message, 'loading', 0); // 不自动消失
  }

  // 更新加载状态toast为成功状态
  function updateToastToSuccess(toastId, message) {
    const toast = document.getElementById(toastId);
    if (toast) {
      toast.className = 'toast success show';
      const iconElement = toast.querySelector('.toast-icon');
      const messageElement = toast.querySelector('.toast-message');
      if (iconElement) iconElement.textContent = '✅';
      if (messageElement) messageElement.textContent = message;

      // 3秒后自动移除
      setTimeout(() => {
        removeToast(toastId);
      }, 3000);
    }
  }

  // 更新加载状态toast为错误状态
  function updateToastToError(toastId, message) {
    const toast = document.getElementById(toastId);
    if (toast) {
      toast.className = 'toast error show';
      const iconElement = toast.querySelector('.toast-icon');
      const messageElement = toast.querySelector('.toast-message');
      if (iconElement) iconElement.textContent = '❌';
      if (messageElement) messageElement.textContent = message;

      // 5秒后自动移除
      setTimeout(() => {
        removeToast(toastId);
      }, 5000);
    }
  }

  // 兼容原有的showStatus函数
  function showStatus(message, type) {
    showToast(message, type);
  }

  // 全局函数，供HTML中的onclick使用
  window.removeToast = removeToast;

  // 加载待处理的内容
  async function loadPendingContent() {
    try {
      // 检查是否在Chrome扩展环境中
      if (typeof chrome === 'undefined' || !chrome.storage) {
        console.warn('⚠️ 非Chrome扩展环境，跳过内容加载');
        return;
      }

      const result = await chrome.storage.local.get('pendingContent');
      if (result.pendingContent) {
        const { selectedText, formattedText, pageTitle: title, pageUrl: url } = result.pendingContent;

        if (selectedText) {
          // 覆盖模式：完全替换当前内容
          // 优先使用格式化文本，如果没有则使用原始文本
          contentText.value = formattedText || selectedText;

          if (title && url) {
            pageTitle.textContent = title;
            updateUrlDisplay(url);
            contentInfo.style.display = 'block';
          } else {
            // 如果没有页面信息，隐藏信息区域
            contentInfo.style.display = 'none';
          }

          // 聚焦到文本区域，方便用户编辑
          contentText.focus();
        }

        // 清理临时数据
        await chrome.storage.local.remove('pendingContent');
      }
    } catch (error) {
      console.error('加载内容失败:', error);
      showStatus('加载内容失败: ' + error.message, 'error');
    }
  }

  // 监听存储变化，实现实时内容更新（覆盖模式）
  function setupStorageListener() {
    // 检查是否在Chrome扩展环境中
    if (typeof chrome === 'undefined' || !chrome.storage) {
      console.warn('⚠️ 非Chrome扩展环境，跳过存储监听器设置');
      return;
    }

    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'local' && changes.pendingContent) {
        // 检测到新的待处理内容，立即加载并覆盖当前内容
        if (changes.pendingContent.newValue) {
          // 添加视觉反馈，让用户知道内容已更新
          contentText.style.transition = 'background-color 0.3s ease';
          contentText.style.backgroundColor = '#e8f0fe';

          loadPendingContent();

          // 短暂高亮后恢复正常
          setTimeout(() => {
            contentText.style.backgroundColor = '';
          }, 500);
        }
      }

      // 监听配置变化，实时更新AI按钮显示状态
      if (namespace === 'sync') {
        // 检查是否有AI相关配置变化（包括新的统一配置管理系统）
        const aiConfigKeys = [
          // 旧版本配置键
          'openrouter_api_key', 'doubao_api_key', 'openai_api_key', 'anthropic_api_key', 'ai_provider', 'ai_model', 'aiEnabled',
          // 新版本配置键
          'aiConfig', 'providerConfigs'
        ];
        const hasAIConfigChange = aiConfigKeys.some(key => changes[key]);

        // 检查是否有Flomo配置变化
        const hasFlomoConfigChange = changes.flomoApiUrl || changes.flomoConfig;

        if (hasAIConfigChange) {
          console.log('🔄 检测到AI配置变化，重新检查AI状态');
          // 重置AI配置状态，强制重新检查
          aiConfigInitialized = false;
          AI_CONFIG = null;

          // 清除统一配置管理器的缓存
          (async () => {
            try {
              const { configManager } = await import('./src/config/config-manager.js');
              if (configManager && configManager.clearCache) {
                configManager.clearCache();
                console.log('🔄 已清除配置管理器缓存');
              }
            } catch (error) {
              console.warn('⚠️ 清除配置缓存失败:', error);
            }
          })();

          // 异步检查并更新AI按钮状态
          setTimeout(async () => {
            try {
              await initializeAIConfig();
              console.log('✅ AI配置已更新');
              await updateAIButtonsVisibility(true);
            } catch (error) {
              console.warn('⚠️ AI配置更新后仍不可用:', error.message);
              await updateAIButtonsVisibility(false);
            }
          }, 200);
        }

        if (hasFlomoConfigChange) {
          console.log('🔄 检测到Flomo API URL变化:', changes.flomoApiUrl.newValue);
        }
      }
    });
  }

  // 设置消息监听器，处理来自popup的AI配置更新
  function setupMessageListener() {
    // 检查是否在Chrome扩展环境中
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      console.warn('⚠️ 非Chrome扩展环境，跳过消息监听器设置');
      return;
    }

    chrome.runtime.onMessage.addListener(async (message, _sender, sendResponse) => {
      if (message.type === 'CONFIG_UPDATED' || message.type === 'AI_CONFIG_UPDATED') {
        console.log('📡 收到配置更新消息:', message.data);

        try {
          let hasAIConfigChange = false;
          let hasFlomoConfigChange = false;

          // 检查AI配置变化
          if (message.data.aiEnabled !== undefined ||
            message.data.aiProvider ||
            message.data.aiApiKey ||
            message.data.aiModel) {
            hasAIConfigChange = true;
            // 重置AI配置状态，强制重新检查
            aiConfigInitialized = false;
            AI_CONFIG = null;

            // 清除统一配置管理器的缓存
            try {
              const { configManager } = await import('./src/config/config-manager.js');
              if (configManager && configManager.clearCache) {
                configManager.clearCache();
                console.log('🔄 已清除配置管理器缓存');
              }
            } catch (error) {
              console.warn('⚠️ 清除配置缓存失败:', error);
            }
          }

          // 检查Flomo配置变化
          if (message.data.flomoApiUrl !== undefined) {
            hasFlomoConfigChange = true;
            console.log('🔄 检测到Flomo API URL变化:', message.data.flomoApiUrl);
          }

          // 处理AI配置更新
          if (hasAIConfigChange) {
            // 延迟一点时间确保配置已经保存
            setTimeout(async () => {
              try {
                await initializeAIConfig();
                console.log('✅ AI配置已更新');
                await updateAIButtonsVisibility();
              } catch (error) {
                console.warn('⚠️ AI配置更新失败:', error.message);
                await updateAIButtonsVisibility(false);
              }
            }, 200);
          }

          // 处理Flomo配置更新
          if (hasFlomoConfigChange) {
            console.log('✅ Flomo配置变化已记录，将在下次保存时生效');
          }

          sendResponse({ success: true });
        } catch (error) {
          console.error('❌ 配置更新处理失败:', error);
          sendResponse({ success: false, error: error.message });
        }

        return true; // 保持消息通道开放以支持异步响应
      }
    });
  }

  // 保存到Flomo
  async function saveToFlomo() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请输入要保存的内容', 'error');
      return;
    }

    // 构建最终保存内容，包含引用信息
    let finalContent = content;

    // 检查是否有引用信息需要附加
    const fullUrl = pageUrl.getAttribute('data-full-url') || pageUrl.textContent;
    const hasPageInfo = pageTitle.textContent && fullUrl &&
      pageTitle.textContent !== '未知页面' && fullUrl !== '未知链接';

    if (hasPageInfo) {
      finalContent += '\n---\n' + pageTitle.textContent + '\n' + fullUrl;
    }

    saveButton.disabled = true;
    saveButton.classList.add('loading');
    saveButton.textContent = '保存中...';

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'saveToFlomo',
        content: finalContent
      });

      if (response.success) {
        showStatus('内容已成功保存到 Flomo！', 'success');
        contentText.value = '';
        contentInfo.style.display = 'none';

        // 检查侧边栏配置，决定是否自动关闭
        try {
          const result = await chrome.storage.sync.get('sidepanelConfig');
          const sidepanelConfig = {
            autoClose: true, // 默认保存后自动关闭
            autoCloseDelay: 1000, // 1秒后关闭
            ...(result.sidepanelConfig || {})
          };

          if (sidepanelConfig.autoClose) {
            const delay = sidepanelConfig.autoCloseDelay || 1000;
            setTimeout(() => {
              window.close();
            }, delay);
          }
        } catch (error) {
          console.error('获取侧边栏配置失败:', error);
          // 默认行为：1秒后关闭
          setTimeout(() => {
            window.close();
          }, 1000);
        }
      } else {
        showStatus(response.error || '保存失败', 'error');
      }
    } catch (error) {
      showStatus('保存失败: ' + error.message, 'error');
    } finally {
      saveButton.disabled = false;
      saveButton.classList.remove('loading');
      saveButton.textContent = '保存到 Flomo';
    }
  }

  // 检查AI功能是否可用（综合开关状态和配置状态）
  async function checkAIAvailability() {
    const aiEnabled = await checkAIEnabledStatus();

    if (!aiEnabled) {
      showToast('AI功能已关闭，请在扩展设置中开启', 'error');
      return false;
    }

    const isConfigured = await ensureAIConfig();
    if (!isConfigured) {
      showToast('AI配置不完整，请在扩展设置中完善配置', 'error');
      return false;
    }

    return true;
  }

  // 简单的AI功能 - 生成标签
  async function generateTags() {
    const content = contentText.value.trim();

    if (!content) {
      showToast('请先输入内容', 'error');
      return;
    }

    // 检查AI功能是否可用
    if (!(await checkAIAvailability())) {
      return;
    }

    // 检查是否有其他AI操作正在进行
    if (currentAIOperation) {
      showStatus(`请等待 ${currentAIOperation} 完成后再执行其他AI功能`, 'error');
      return;
    }

    // 设置当前操作状态
    currentAIOperation = '标签生成';

    generateTagsBtn.disabled = true;
    generateTagsBtn.classList.add('loading');

    try {
      // 简单的标签生成逻辑
      const tags = await simpleTagGeneration(content);
      contentText.value = content + '\n\n' + tags;
      showStatus('标签已生成', 'success');
    } catch (error) {
      showStatus('标签生成失败', 'error');
    } finally {
      generateTagsBtn.disabled = false;
      generateTagsBtn.classList.remove('loading');
      // 清除当前操作状态
      currentAIOperation = null;
    }
  }

  // 简单的翻译功能
  async function translateText() {
    const content = contentText.value.trim();

    if (!content) {
      showToast('请先输入内容', 'error');
      return;
    }

    // 检查AI功能是否可用
    if (!(await checkAIAvailability())) {
      return;
    }

    // 检查是否有其他AI操作正在进行
    if (currentAIOperation) {
      showStatus(`请等待 ${currentAIOperation} 完成后再执行其他AI功能`, 'error');
      return;
    }

    // 设置当前操作状态
    currentAIOperation = '翻译';

    translateBtn.disabled = true;
    translateBtn.classList.add('loading');

    try {
      // 检测语言并翻译
      const isChineseText = /[\u4e00-\u9fff]/.test(content);
      const translatedText = await simpleTranslation(content, isChineseText);
      contentText.value = content + '\n\n---\n\n' + translatedText;
      showStatus('翻译已完成', 'success');
    } catch (error) {
      showStatus('翻译失败', 'error');
    } finally {
      translateBtn.disabled = false;
      translateBtn.classList.remove('loading');
      // 清除当前操作状态
      currentAIOperation = null;
    }
  }

  // 动态AI配置（从统一配置管理器获取）
  let AI_CONFIG = null;
  let aiConfigInitialized = false;

  // 确保AI配置可用
  async function ensureAIConfig() {
    if (!aiConfigInitialized) {
      try {
        await initializeAIConfig();
        aiConfigInitialized = true;
      } catch (error) {
        console.warn('AI配置未就绪:', error.message);
        return false;
      }
    }
    return AI_CONFIG !== null;
  }

  // 检查AI配置并更新按钮显示状态
  async function checkAndUpdateAIStatus() {
    const isConfigured = await ensureAIConfig();
    await updateAIButtonsVisibility(isConfigured);
    return isConfigured;
  }

  // 初始化AI配置
  async function initializeAIConfig() {
    try {
      // 导入配置适配器
      const { legacyAdapter } = await import('./src/config/adapters/legacy-adapter.js');
      AI_CONFIG = await legacyAdapter.getAIConfig();

      // 验证配置的有效性
      if (!AI_CONFIG || !AI_CONFIG.apiUrl || !AI_CONFIG.apiKey) {
        throw new Error('AI配置无效：缺少必要的API地址或密钥');
      }

      console.log('✅ AI配置已从统一配置管理器加载');
    } catch (error) {
      console.error('❌ 无法加载AI配置:', error);
      // 不再使用硬编码的默认配置，直接抛出错误
      AI_CONFIG = null;
      throw new Error(`AI配置加载失败: ${error.message}`);
    }
  }

  // 使用硅基流动AI生成标签
  async function simpleTagGeneration(text) {
    try {
      // 检查AI配置是否有效（此时应该已经通过ensureAIConfig检查）
      if (!AI_CONFIG || !AI_CONFIG.apiUrl || !AI_CONFIG.apiKey) {
        throw new Error('AI配置无效：缺少API地址或密钥');
      }

      // 获取标签生成Prompt模板
      let prompt;
      try {
        const { legacyAdapter } = await import('./src/config/adapters/legacy-adapter.js');
        prompt = legacyAdapter.getTagGenerationPrompt(text, 'structured');
      } catch (error) {
        console.warn('⚠️ 无法加载Prompt模板，使用默认模板:', error);
        // 降级到默认模板
        prompt = `你是一个顶级的知识管理和标签系统专家。你的任务是分析给定的文本，并生成一个包含相关标签的 JSON 数组。

严格遵守以下规则：
1.  **内容分析**：深入理解文本，提取核心概念、关键实体和主题。
2.  **层级优先**：尽可能创建有意义的层级标签，格式为 "父标签/子标签"。
3.  **简洁准确**：标签必须简明扼要，并与原文内容紧密相关。
4.  **语言一致**：标签的语言应与输入文本的语言保持一致。
5.  **数量控制**：生成 3 到 5 个标签。
6.  **格式要求**：必须返回一个格式正确的 JSON 字符串数组 (`["tag1", "tag2", "tag3"]`)，不包含任何其他解释。

### 示例 ###
输入文本: "OpenAI 宣布其最新的旗舰模型 GPT-4o 现已通过 API 向所有开发者开放。该模型在处理多模态输入方面表现出色，并且响应速度更快、成本更低。"
输出: ["技术/AI/大语言模型", "公司/OpenAI", "产品/GPT-4o", "技术/多模态"]

### 待处理 ###
输入文本: """
${text}
"""
输出:`;
      }
      const response = await callSiliconFlowAPI(prompt);

      // if (response) {
      //   const tags = response.split('，').map(tag => tag.trim()).filter(tag => tag);
      //   return '#' + tags.slice(0, 6).join(' #');
      // } else {
      //   throw new Error('AI响应为空');
      // }
      if (!response || response.trim() === '') {
        throw new Error('AI响应为空');
      }

      return response.trim();
    } catch (error) {
      console.error('AI标签生成失败:', error);
      // 不再使用本地关键词提取作为降级，直接抛出错误
      throw new Error(`标签生成失败: ${error.message}`);
    }
  }

  // 使用统一配置的AI翻译
  async function simpleTranslation(text, isChinese) {
    try {
      // 检查AI配置是否有效（此时应该已经通过ensureAIConfig检查）
      if (!AI_CONFIG || !AI_CONFIG.apiUrl || !AI_CONFIG.apiKey) {
        throw new Error('AI配置无效：缺少API地址或密钥');
      }

      // 获取翻译Prompt模板
      let prompt;
      try {
        const { legacyAdapter } = await import('./src/config/adapters/legacy-adapter.js');
        prompt = legacyAdapter.getTranslationPrompt(text);
      } catch (error) {
        console.warn('⚠️ 无法加载Prompt模板，使用默认模板:', error);
        // 降级到默认模板
        const targetLang = isChinese ? '英文' : '中文';
        prompt = `请将以下内容翻译成${targetLang}，保持原意，只返回翻译结果：\n\n${text}`;
      }

      const response = await callSiliconFlowAPI(prompt);

      if (!response || response.trim() === '') {
        throw new Error('AI响应为空');
      }

      return response.trim();
    } catch (error) {
      console.error('AI翻译失败:', error);
      // 不再使用占位符作为降级，直接抛出错误
      throw new Error(`翻译失败: ${error.message}`);
    }
  }

  // 调用硅基流动API
  async function callSiliconFlowAPI(prompt) {
    try {
      const response = await fetch(AI_CONFIG.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_CONFIG.apiKey}`
        },
        body: JSON.stringify({
          model: AI_CONFIG.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: AI_CONFIG.temperature,
          max_tokens: AI_CONFIG.max_tokens,
          stream: false
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorData.error?.message || '未知错误'}`);
      }

      const data = await response.json();

      if (data.choices && data.choices.length > 0) {
        return data.choices[0].message.content;
      } else {
        throw new Error('API响应格式错误');
      }
    } catch (error) {
      console.error('硅基流动API调用失败:', error);
      throw error;
    }
  }



  // 事件监听
  saveButton.addEventListener('click', saveToFlomo);
  generateTagsBtn.addEventListener('click', generateTags);
  translateBtn.addEventListener('click', translateText);

  // 初始化
  initToastContainer();
  await loadPendingContent();

  // AI配置初始化改为可选，不阻塞基本功能
  try {
    await initializeAIConfig();
    console.log('✅ AI配置已就绪');
    await updateAIButtonsVisibility(true);
  } catch (error) {
    console.warn('⚠️ AI配置未就绪，AI功能将不可用:', error.message);
    AI_CONFIG = null;
    await updateAIButtonsVisibility(false);
  }

  // 设置存储监听器，实现覆盖模式的实时更新
  setupStorageListener();

  // 设置消息监听器，处理来自popup的AI配置更新
  setupMessageListener();
});
