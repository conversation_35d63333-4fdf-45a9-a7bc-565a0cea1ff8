
// 简洁的后台脚本 - 只处理核心功能

// 安装时创建右键菜单
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: "save-to-flomo",
    title: "保存到 Flomo",
    contexts: ["selection"]
  });
});

// 右键菜单点击处理
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  if (info.menuItemId === "save-to-flomo") {
    try {
      // 打开侧边栏
      await chrome.sidePanel.open({ tabId: tab.id });

      // 获取页面信息和格式化的选中内容
      const [pageResult] = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: () => ({
          title: document.title,
          url: window.location.href
        })
      });

      // 获取格式化的选中内容
      let formattedText = info.selectionText; // 默认使用原始文本

      try {
        const [contentResult] = await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          function: () => {
            // 重新获取当前选中内容的格式化版本
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const container = document.createElement('div');
              container.appendChild(range.cloneContents());

              // 使用与content.js相同的格式化逻辑
              return window.htmlToFormattedText ? window.htmlToFormattedText(container) : null;
            }
            return null;
          }
        });

        if (contentResult.result) {
          formattedText = contentResult.result;
        }
      } catch (error) {
        console.warn('获取格式化文本失败，使用原始文本:', error);
      }

      // 存储选中的内容和页面信息（覆盖模式）
      await chrome.storage.local.set({
        'pendingContent': {
          selectedText: info.selectionText, // 保持向后兼容
          formattedText: formattedText, // 新的格式化文本
          pageTitle: pageResult.result.title,
          pageUrl: pageResult.result.url,
          timestamp: Date.now(),
          // 添加一个标识符，确保每次都是新的内容
          id: Math.random().toString(36).substring(2, 11)
        }
      });
    } catch (error) {
      console.error('处理选中内容失败:', error);
    }
  }
});

// 处理来自侧边栏的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.action === 'saveToFlomo') {
    saveToFlomo(message.content)
      .then(result => sendResponse({ success: true, result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // 保持消息通道开放
  }
});

// 保存内容到Flomo
async function saveToFlomo(content) {
  const { flomoApiUrl } = await chrome.storage.sync.get('flomoApiUrl');

  if (!flomoApiUrl) {
    throw new Error('请先配置 Flomo API 地址');
  }

  const response = await fetch(flomoApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      content: content.trim()
    })
  });

  if (!response.ok) {
    throw new Error(`保存失败: ${response.status} ${response.statusText}`);
  }

  return await response.json();
}
