:root {
  --primary-color: #4F46E5;
  --secondary-color: #64748B;
  --success-color: #10B981;
  --error-color: #EF4444;
  --warning-color: #F59E0B;
  --background-color: #F8FAFC;
  --card-background: #FFFFFF;
  --border-color: #E2E8F0;
  --text-primary: #1E293B;
  --text-secondary: #64748B;
  --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.2s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  width: 400px;
  min-height: 600px;
  max-height: 700px;
  overflow-y: auto;
}

.container {
  padding: 20px;
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 24px;
}

.header h1 {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 元数据卡片 */
.metadata-card {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.metadata-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.metadata-item:last-child {
  margin-bottom: 0;
}

.metadata-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.metadata-value {
  font-size: 14px;
  color: var(--text-primary);
  word-break: break-all;
}

.metadata-value.url {
  color: var(--primary-color);
  text-decoration: none;
}

.metadata-value.url:hover {
  text-decoration: underline;
}

/* 格式选择部分 */
.format-section {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.format-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.format-options {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.format-option {
  flex: 1;
  padding: 8px 12px;
  background: var(--background-color);
  border-radius: 6px;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.format-option:hover {
  background: #F1F5F9;
}

.format-option.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.format-preview {
  font-size: 13px;
  color: var(--text-secondary);
  padding: 8px 12px;
  background: var(--background-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

/* AI部分 */
.ai-section {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.ai-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.streaming-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--text-secondary);
}

.streaming-toggle input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.ai-functions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.ai-function {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px;
  background: var(--background-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.ai-function:hover {
  background: #F1F5F9;
  transform: translateY(-1px);
}

.ai-function-icon {
  font-size: 18px;
}

.ai-function-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
  text-align: center;
}

/* AI进度 */
.ai-progress {
  display: none;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: var(--background-color);
  border-radius: var(--border-radius);
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #E2E8F0;
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.cancel-streaming-btn {
  margin-top: 8px;
  padding: 4px 8px;
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

/* AI结果 */
.ai-results {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ai-result {
  padding: 12px;
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.ai-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.ai-result-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.ai-apply-btn {
  padding: 4px 8px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.ai-result-content {
  font-size: 13px;
  color: var(--text-primary);
  line-height: 1.4;
}

.ai-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.ai-tag {
  padding: 2px 8px;
  background: #E0E7FF;
  color: var(--primary-color);
  border-radius: 12px;
  font-size: 12px;
  cursor: pointer;
  transition: var(--transition);
}

.ai-tag:hover {
  background: #C7D2FE;
}

.ai-tag.added {
  background: var(--primary-color);
  color: white;
}

/* 编辑部分 */
.edit-section {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.edit-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.textarea-container {
  position: relative;
}

#content-textarea {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  transition: var(--transition);
}

#content-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.char-count {
  position: absolute;
  bottom: 8px;
  right: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

/* 底部操作区域 */
.bottom-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cache-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.cache-stats {
  font-size: 13px;
  color: var(--text-secondary);
}

.cache-actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338CA;
}

.btn-secondary {
  background: var(--background-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: #F1F5F9;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

#cancel-btn {
  flex: 1;
}

#save-btn {
  flex: 2;
}

/* 状态消息 */
.status-message {
  padding: 12px 16px;
  border-radius: var(--border-radius);
  font-size: 13px;
  text-align: center;
  display: none;
}

.status-message.success {
  background: #D1FAE5;
  color: #065F46;
  border: 1px solid #A7F3D0;
}

.status-message.error {
  background: #FEE2E2;
  color: #991B1B;
  border: 1px solid #FECACA;
}

.status-message.info {
  background: #DBEAFE;
  color: #1E40AF;
  border: 1px solid #BFDBFE;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  gap: 16px;
}

.empty-state-icon {
  font-size: 48px;
}

.empty-state-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.empty-state-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  gap: 16px;
}

.error-state-icon {
  font-size: 48px;
}

.error-state-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--error-color);
}

.error-state-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 420px) {
  body {
    width: 100%;
  }

  .container {
    padding: 16px;
  }

  .ai-functions {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  #cancel-btn,
  #save-btn {
    flex: none;
  }
}

/* AI工具按钮样式 */
.ai-tools {
  display: flex;
  gap: 12px;
  margin: 16px 0;
  flex-wrap: wrap;
}

.ai-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.ai-btn:hover::before {
  left: 100%;
}

.ai-btn-tags {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.ai-btn-tags:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.ai-btn-translate {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.ai-btn-translate:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.ai-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.ai-btn:disabled::before {
  display: none;
}

/* 配置不完整状态样式 */
.ai-btn.config-incomplete {
  opacity: 0.7;
  position: relative;
  border: 2px dashed rgba(255, 193, 7, 0.6);
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
}

.ai-btn.config-incomplete::after {
  content: '⚠️';
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ffc107;
  color: #212529;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ai-btn.config-incomplete:hover {
  opacity: 0.9;
  border-color: #ffc107;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.2));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.ai-btn.loading {
  pointer-events: none;
}

.ai-btn.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-color);
}

::-webkit-scrollbar-thumb {
  background: #CBD5E1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94A3B8;
}