# Save to Flomo - Chrome 扩展

一个简洁高效的 Chrome 扩展，用于将网页内容快速保存到 Flomo 笔记应用，支持 AI 智能处理。

## ✨ 功能特性

### 🚀 核心功能

- **快速保存**: 选中网页文字后，通过右键菜单或侧边栏快速保存到 Flomo
- **智能格式处理**: 自动适配 Flomo 显示需求，保留段落结构，其他格式转为普通文本
- **多种保存方式**: 右键菜单直接保存 | 侧边栏编辑保存

#### 📝 格式处理说明

**保留的格式**：

- 段落结构和换行
- 基本的内容层级

**转换为普通文本的格式**：

- 标题（H1-H6）→ 普通文本
- 列表（有序/无序）→ 保持缩进的普通文本
- 文本样式（粗体、斜体、下划线等）→ 普通文本
- 代码块和行内代码 → 普通文本
- 链接 → 只保留链接文本
- 引用块 → 普通文本

这样处理确保内容在 Flomo 中完美显示，避免格式语法干扰。

### 🤖 AI 增强功能

- **智能标签生成**: 使用 AI 自动为内容生成 3-6 个相关标签
- **内容翻译**: 支持中英文智能互译
- **多 AI 模型支持**: SiliconFlow、OpenRouter、DeepSeek、Moonshot AI

### 🎨 用户体验

- **侧边栏自动关闭**: 保存成功后 1 秒自动关闭（可配置）
- **覆盖模式**: 多次选择文本时，新内容会完全替换侧边栏中的旧内容
- **实时更新**: 侧边栏已打开时，新选择的内容会立即更新显示
- **AI 功能互斥**: 防止多个 AI 操作同时进行，避免数据冲突
- **统一配置管理**: 集中管理所有配置项，支持环境变量和用户设置
- **完善错误处理**: 智能降级机制，确保功能稳定性

## 📦 安装使用

### 1. 安装扩展

```bash
1. 下载扩展文件
2. 打开 chrome://extensions/
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择扩展文件夹
```

### 2. 配置 API

```bash
1. 点击扩展图标打开配置页面
2. 设置Flomo API地址（必需）
3. 配置AI服务（可选）
```

### 3. 使用方法

- **快速保存**: 选中文字 → 右键菜单 → "保存到 Flomo"
- **编辑保存**: 选中文字 → 点击扩展图标 → 侧边栏编辑 → 保存
- **覆盖模式**: 多次选择文本时，新内容会自动替换侧边栏中的内容，确保始终显示最新选择

## 🔧 配置说明

### 必需配置

#### 1. Flomo API 配置

- 获取你的 Flomo API 地址
- 在扩展设置中输入 API 地址

#### 2. AI 服务配置 ⚠️ **重要更新**

**从 v0.4 版本开始，需要用户自行配置 AI 服务的 API 密钥**

支持的 AI 服务商：

| 服务商          | 优势                                                       | 免费额度         | API 密钥格式            |
| --------------- | ---------------------------------------------------------- | ---------------- | ----------------------- |
| **OpenRouter**  | 🎯 推荐<br/>• 聚合多个模型<br/>• 有免费模型<br/>• 配置简单 | 有限的免费模型   | `sk-or-v1-xxxxx`        |
| **DeepSeek**    | • 性价比高<br/>• 中文支持好<br/>• 编程能力强               | 新用户有免费额度 | `sk-xxxxxxxxxx` (48 位) |
| **硅基流动**    | • 开源模型<br/>• 中文服务商<br/>• 稳定性好                 | 新用户有免费额度 | `sk-xxxxxxxxxx` (48 位) |
| **Moonshot AI** | • Kimi 品牌<br/>• 长文本支持<br/>• 中文优化                | 新用户有免费额度 | `sk-xxxxxxxxxx` (48 位) |

#### 获取 API 密钥

1. **OpenRouter**

   - 访问 [OpenRouter](https://openrouter.ai/)
   - 注册账号并获取 API 密钥
   - 格式：`sk-or-v1-xxxxxxxxx`

2. **DeepSeek**

   - 访问 [DeepSeek](https://platform.deepseek.com/)
   - 注册账号并获取 API 密钥
   - 格式：`sk-xxxxxxxxxxxxxxxx` (48 位字符)

3. **硅基流动**

   - 访问 [硅基流动](https://siliconflow.cn/)
   - 注册账号并获取 API 密钥
   - 格式：`sk-xxxxxxxxxxxxxxxx` (48 位字符)

4. **Moonshot AI**
   - 访问 [Moonshot AI](https://platform.moonshot.cn/)
   - 注册账号并获取 API 密钥
   - 格式：`sk-xxxxxxxxxxxxxxxx` (48 位字符)

#### 配置步骤

1. 点击扩展图标打开设置面板
2. 在 "AI 配置" 部分：
   - 选择你的 AI 服务商
   - 输入对应的 API 密钥
   - 选择要使用的模型
3. 在 "Flomo 配置" 部分输入你的 Flomo API 地址
4. 点击 "保存设置"

### ⚠️ 注意事项

- API 密钥会安全存储在浏览器本地，不会上传到任何服务器
- 确保 API 密钥格式正确，否则无法正常使用 AI 功能
- 不同服务商的定价和使用限制不同，请查看对应平台的说明

### 侧边栏配置

- **自动关闭**: 保存后是否自动关闭侧边栏（默认开启）
- **关闭延迟**: 自动关闭的延迟时间（默认 1000ms）

## 🏗️ 项目结构

```
chrome-plugin-save-to-flomo/
├── manifest.json              # 扩展配置文件
├── background.js              # 后台服务脚本
├── content.js                 # 内容脚本
├── popup.html/js              # 配置页面
├── popup-config.js            # 配置模式定义
├── sidepanel.html/js/css      # 侧边栏页面
├── ai-config.js               # AI配置管理
├── test-format.js             # 格式测试脚本
├── icons/                     # 扩展图标目录
│   ├── icon16.png             # 16x16 图标
│   ├── icon32.png             # 32x32 图标
│   ├── icon48.png             # 48x48 图标
│   └── icon128.png            # 128x128 图标
└── src/config/                # 统一配置管理系统
    ├── config-manager.js      # 核心配置管理器
    ├── config-schema.js       # 配置模式定义
    ├── prompts.js             # AI提示词模板管理
    └── adapters/
        └── legacy-adapter.js  # 向后兼容适配器
```

## 🧪 测试

使用 `test-format.js` 进行格式转换测试：

- ✅ 文本格式转换测试
- ✅ HTML 处理测试
- ✅ 段落结构保持测试

## 🔧 开发指南

### 开发环境设置

```bash
1. 克隆项目到本地
2. 在Chrome中加载扩展（开发者模式）
3. 修改代码后重新加载扩展
4. 使用test-format.js验证格式转换功能
```

### 核心技术特性

- **统一配置管理**: 优先级管理（环境变量 > 用户设置 > 默认值）
- **模板化提示词**: 可配置的 AI 提示词模板，支持变量替换
- **向后兼容**: 支持旧版本配置格式的自动迁移
- **智能降级**: 配置加载失败时的降级机制

## 📝 更新日志

### v0.4.3 (当前版本)

- 🧹 整理项目文件结构，删除空目录和冗余文件
- 📝 整合分散的文档到 README.md，提升维护性
- 🔧 隐藏高级配置选项，简化用户界面
- 🎯 优化格式处理逻辑，仅保留段落格式
- 📄 更新项目结构说明和使用指南

### v0.4

- 🔧 重构统一配置管理系统
- 🤖 统一 AI 提示词模板管理
- 🎨 优化用户体验和界面
- 📦 精简项目结构，删除冗余代码
- ✅ 完善测试体系

### v0.1

- 🚀 基础保存功能
- 🤖 AI 增强功能
- 🎨 侧边栏支持

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
